from .util import create_message_chunks

MESSAGE = """
Hello World

# Mermaid

# GFM

## Autolink literals

www.example.com, https://example.com, and <EMAIL>.

## Table

| a | b  |  c |  d  |
|---|----|----|-----|
| - | :- | -: | :-: |
| 1 |  2 |  3 |  4  |


"""

error_message = [
    {
        "data": '{"error_type": "ValueError", "error_message": "大模型无响应，正在重试..."}',
        "is_last": True,
        "package_type": 3,
        "is_new_package": True,
        "is_error": True,
    }
]

network_error_message = [
    {
        "is_network_error": True,
    }
]


def create_error_message_chunks():
    return (
        create_message_chunks(MESSAGE) + error_message + create_message_chunks(MESSAGE) + network_error_message + create_message_chunks(MESSAGE)
    )
