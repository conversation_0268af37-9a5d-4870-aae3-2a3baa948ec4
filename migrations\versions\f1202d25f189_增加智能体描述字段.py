"""增加智能体描述字段

Revision ID: f1202d25f189
Revises: 4a96f3ae4dc5
Create Date: 2025-06-09 15:01:43.872621

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'f1202d25f189'
down_revision: Union[str, None] = '4a96f3ae4dc5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_agent', sa.Column('agent_desc',sa.TEXT(), nullable=True))
    op.alter_column('ai_agent', 'target_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=False)
    op.alter_column('ai_agent', 'base_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'api_key',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'router_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'status',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ai_agent', 'status',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('ai_agent', 'router_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'api_key',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'base_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'target_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=True)
    op.drop_column('ai_agent', 'agent_desc')
    # ### end Alembic commands ###
