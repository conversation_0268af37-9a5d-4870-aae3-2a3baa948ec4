# RedisUtil 工具类
# <AUTHOR> xiangjh
import redis
import logging
import json
from core.config.app_config import config
logger = logging.getLogger(__name__)

class RedisUtil:
    def __init__(self):
        redis_config = config.redis
        self.host = redis_config.host
        self.port = redis_config.port
        self.password = redis_config.password
        self.db = redis_config.db
        self.decode_responses = redis_config.decode_responses

        self.client = redis.StrictRedis(
            host=self.host,
            port=self.port,
            password=self.password,
            db=self.db,
            decode_responses=self.decode_responses
        )

    def get_user(self, token: str):
        """
        从 Redis 中获取用户信息
        :param token: 授权 token
        :return: UserVO 字段字典 或 None
        """
        try:
            cached = self.client.get(f"agentauth:{token}")
            if cached:
                return json.loads(cached)
            return None
        except Exception as e:
            logger.warning(f"Redis 获取用户失败: {e}")
            return None

    def set_user(self, token: str, user_data: dict, expire_seconds=30000):
        """
        将用户信息存入 Redis
        :param token: 授权 token
        :param user_data: 用户数据字典
        :param expire_seconds: 过期时间（秒）
        """
        try:
            key = f"agentauth:{token}"
            value = json.dumps(user_data)
            self.client.setex(key, expire_seconds, value)
            
        except Exception as e:
            logger.error(f"Redis 缓存用户失败: {e}")

redis_client = RedisUtil()