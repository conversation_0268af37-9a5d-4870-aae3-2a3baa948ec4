[project]
name = "agent-server"
version = "1.0.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "alembic>=1.15.2",
    "asyncpg>=0.30.0",
    "dotenv>=0.9.9",
    "fastapi[standard]>=0.115.11",
    "httpx>=0.28.1",
    "langchain-community>=0.3.24",
    "langchain[openai]>=0.3.25",
    "langgraph>=0.1.0",
    "loguru>=0.7.3",
    "mcp>=1.9.0",
    "pillow>=11.1.0",
    "psycopg[binary,pool]>=3.2.6",
    "pylint>=3.3.6",
    "redis>=6.1.0",
    "sqlalchemy>=2.0.41",
    "sqlmodel>=0.0.24",
]
[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
default = true
