from fastapi import APIRouter, Depends, HTTPEx<PERSON>, Query,Header

from typing import Annotated
import requests
import logging
from core.config.app_config import config
from core.vo.user_vo import UserVO
from core.config.security import check_user
from core.services.database import db_manager
from core.services.database.crud.ai_agent_dao import ai_agent_crud

router = APIRouter(prefix="/agent")

logger = logging.getLogger(__name__)

@router.get("")
async def query_agents(authorization: Annotated[str | None, Header()] = None):
    """
        查询所有agent列表

        Returns:
            agent列表
    """
     
    try:
        agents = await ai_agent_crud.query_agents()
        # 首先获取所有agent数据中的图标地址
        agentIconIds = []
        for agent in agents:
            if agent.icon:
                agentIconIds.append(agent.icon)
        print(agentIconIds)
        # 调用span后端图片预览地址
        if len(agentIconIds):
            full_config = config.full_config
            commonPitcureUrl = full_config.get('commonPitcureUrl')
            logger.info(f"请求图片预览地址配置地址:{commonPitcureUrl}")

            if not commonPitcureUrl:
                raise HTTPException(status_code=500, detail="图片预览地址未配置")
            
            response = requests.post(commonPitcureUrl,json = agentIconIds,headers={
                "Authorization": authorization},timeout=5)
            
            if response.status_code != 200:
                logger.error("获取图片预览地址失败")

            try:
                json_data = response.json()
            except ValueError as ve:
                logger.error(f"JSON解析失败: {ve}")
            #请求成功后将对应的url地址赋值到icon字段上
            ulrs = json_data['data']
            for agent in agents:
                if agent.icon:
                    agent.icon = ulrs[agent.icon]

                # print(agents)
        return {
            "code": 200,
            "data": agents,
            "message": "查询成功",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e
    