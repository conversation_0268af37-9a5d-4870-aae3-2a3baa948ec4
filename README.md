# agent-server

LLM基础后端服务

## 开发

### 安装依赖
```sh
pip install uv

uv sync
```

### 启动服务
```sh
uv run -m main
```

## docker 部署

### 本地构建
```bash
# 构建镜像 PowerShell
# $env:HTTPS_PROXY="http://127.0.0.1:10888"
docker build -t agent-server/latest .

# 保存镜像 WSL 环境
docker save agent-server/latest | gzip > ./docker/agent_server_latest.tar.gz
```

### 服务器部署
```bash
# 解压镜像
gunzip -c agent_server_latest.tar.gz > agent_server_latest.tar

# 删除旧的镜像
docker rmi agent-server/latest

# 加载镜像
docker load < agent_server_latest.tar

# 删除旧容器
sudo docker container stop agent-server
sudo docker container rm agent-server

# 运行容器
sudo docker run \
  --add-host=host.docker.internal:host-gateway \
  -e APP_ENV="prod" \
  -e CONFIG_PATH="/app/config/application.yml" \
  -v /data/agent/app/logs:/app/logs \
  -v /data/agent/app/config:/app/config \
  -p 5801:8000 \
  --name agent-server \
  -d agent-server/latest
```

## 数据库

### 备份

```bash
sudo docker exec -t timescaledb pg_dump -U postgres -Fc agent_db > /data/backups/my_database_$(date +%Y%m%d%H%M%S).dump
```

## Todo List

- [x] 配置文件移到镜像外，通过环境变量配置路径
- [x] log 文件目录
- [ ] 多 Agent 集成架构设计
- [ ] 通用异常判断
- [ ] 通用 Response
- [ ] 打包配置
