"""add_message_rating_fields

Revision ID: 884cb900b6d2
Revises: b36e4ebed07e
Create Date: 2025-06-12 10:42:44.428790

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '884cb900b6d2'
down_revision: Union[str, None] = 'b36e4ebed07e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('message', sa.Column('like_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('message', sa.Column('dislike_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('message', sa.Column('user_rating', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('message', 'user_rating')
    op.drop_column('message', 'dislike_count')
    op.drop_column('message', 'like_count')
    # ### end Alembic commands ###
