# 日志配置
# <AUTHOR> xiangjh

from loguru import logger
import os
from datetime import datetime

# 设置日志输出目录
LOG_DIR = os.path.join(os.getcwd(), "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# 按照日期格式生成日志文件名
def get_log_file_name():
    today = datetime.now().strftime("%Y%m%d")
    return os.path.join(LOG_DIR, f"{today}.log")

_log_file_name = get_log_file_name()
# 配置 Loguru 日志器
logger.add(
    sink=_log_file_name,
    rotation="10 MB",
    retention="7 days",
    enqueue=True,
    encoding="utf-8",
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | {name}:{function}:{line} - {message}",
    level="INFO"
)

# 可选：移除默认控制台输出，仅写入文件
# logger.remove(handler_id=0)

# 可选：添加 stderr 输出
# logger.add(sys.stderr, level="DEBUG")

__all__ = ["logger"]