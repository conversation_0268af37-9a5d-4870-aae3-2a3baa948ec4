# 系统鉴权工具类（通过调用 pre_authorize 和 checkuser 分别实现鉴权和登录验证）
# <AUTHOR> xiangjh
from fastapi import Header, HTTPException, Depends
from typing import Annotated
import requests
import logging

from core.config.app_config import config
from core.vo.user_vo import UserVO
from utils.redis_util import redis_client

logger = logging.getLogger(__name__)

# 依赖注入式的用户认证装饰器
# 从请求头中获取用户令牌，验证用户身份并返回 UserVO 对象

async def get_current_user(authorization: Annotated[str, Header()] = None):
    try:
        full_config = config.full_config
        if not authorization:
            raise HTTPException(status_code=401, detail="用户未认证")
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="无效的认证头")

        token = authorization[7:].strip()
        print(f"获取到用户令牌: {token}")

        check_auth_url = full_config.get('userUrl')
        if not check_auth_url:
            raise HTTPException(status_code=500, detail="认证服务地址未配置")

        response = requests.get(
            check_auth_url,
            headers={
                "Authorization": authorization,
                "Content-Type": "application/json"
            },
            timeout=5
        )

        if response.status_code != 200:
            logger.error(f"认证服务返回非200状态码: {response.status_code}")
            raise HTTPException(status_code=401, detail="认证失败")

        try:
            json_data = response.json()
        except ValueError as ve:
            logger.error(f"JSON解析失败: {ve}")
            raise HTTPException(status_code=500, detail="认证服务返回格式错误")

        data = json_data.get("data", {})
        curr_user = UserVO()
        curr_user.userId = data.get("userId")
        curr_user.username = data.get("username")
        curr_user.nickName = data.get("nickName")
        permissions = data.get("permissions", [])

        if not isinstance(permissions, list):
            logger.warning("权限字段不是列表类型，默认赋空列表")
            permissions = []

        curr_user.permissions = set(permissions)

        # Step 3: 写入 Redis 缓存
        user_cache_data = {
            "userId": curr_user.userId,
            "username": curr_user.username,
            "nickName": curr_user.nickName,
            "permissions": list(curr_user.permissions)
        }
        redis_client.set_user(token, user_cache_data)

        return curr_user

    except requests.exceptions.RequestException as re:
        logger.error(f"请求认证服务失败: {re}")
        raise HTTPException(status_code=503, detail="认证服务不可用")

    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.error(f"获取当前用户时发生异常: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="认证失败!")

# 依赖注入式的权限校验装饰器 
# 接收一个权限集合参数 permissions，表示调用接口所需的权限，若权限不足，则抛出 403 异常；否则返回用户对象

def pre_authorize(permissions: set[str]):
    async def authorize_dependency(user: UserVO = Depends(get_current_user)):
        if not permissions.issubset(user.permissions):
            raise HTTPException(status_code=403, detail="Permission denied")
        return user
    # 返回函数本身，FastAPI 会自动包装成 Depends
    return authorize_dependency

async def check_user_dependency(user: UserVO = Depends(get_current_user)):
    if user is None:
        raise HTTPException(status_code=401, detail="用户未认证")
    return user

# 用户认证装饰器，用于检查用户是否已登录，若未登录则抛出 401 异常；否则返回用户对象

async def check_user(user: UserVO = Depends(check_user_dependency)):
    return user