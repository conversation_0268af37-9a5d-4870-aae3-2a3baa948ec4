from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import httpx

from core.config.app_config import config

router = APIRouter(tags=["Login"])

class LoginPayload(BaseModel):
    username: str
    password: str

@router.post("/login")
async def login(payload: LoginPayload):
    # 调用外部接口登录
    host = config.business_system.host
    port = config.business_system.port
    url = f"http://{host}:{port}/api/oauth/login"
    # 这里可以添加实际的登录逻辑，比如调用外部API等

    data = {
        "username": payload.username,
        "password": payload.password,
        "kaptcha": "",
        "clientId": "client",
        "clientSecret": "secret",
        "loginType": "simple:web",
        "grantType": "password",
    }

    # 使用 httpx 发送 POST 请求
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data)
        if response.status_code == 200:
            data = response.json()
            return {"message": "登录成功", "data": data["data"]}
        else:
            raise HTTPException(status_code=response.status_code, detail="登录失败")
