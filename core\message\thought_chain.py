class ThoughtChainGenerator:
    def __init__(self):
        self.index = -1

    def create(self):
        return """
          <message-embedded>
            <state>
              <set>
                <strategy>replace</strategy>
                <path>thoughtChainItems</path>
                <value>[]</value>
              </set>
            </state>
            <widget>
              <code>@BuildIn/ThoughtChain</code>
              <props>
                <items>{{state.thoughtChainItems}}</newItem>
              </props>
            </widget>
          </message-embedded>
          """
    
    def create_node(self, *, title: str, description: str, content: str, status: str):
        self.index += 1
        return f"""
          <message-embedded>
            <state>
              <set>
                <strategy>replace</strategy>
                <path>thoughtChainItems[{self.index}]</path>
                <value>
                {{
                    "id": "{self.index}",
                    "title": "{title}",
                    "description": "{description}",
                    "content": "{content}",
                    "status": "{status}"
                }}
                </value>
              </set>
            </state>
          </message-embedded>
          """
    
    def update_node_content(self, index: int, content: str):
        return f"""
            <message-embedded>
              <state>
                <set>
                  <strategy>incrementalMerge</strategy>
                  <path>thoughtChainItems[{index}].content</path>
                  <value>{content}</value>
                </set>
              </state>
            </message-embedded>
        """
    
    def update_node(self, *, index: int, title: str, description: str, content: str, status: str ):
        set_list = ""
        if title is not None:
            set_list += f"""
                <set>
                  <strategy>merge</strategy>
                  <path>thoughtChainItems[{index}].title</path>
                  <value>{title}</value>
                </set>
            """
        if description is not None:
            set_list += f"""
                <set>
                  <strategy>merge</strategy>
                  <path>thoughtChainItems[{index}].description</path>
                  <value>{description}</value>
                </set>
            """
        if content is not None:
            set_list += f"""
                <set>
                  <strategy>merge</strategy>
                  <path>thoughtChainItems[{index}].content</path>
                  <value>{content}</value>
                </set>
            """
        if status is not None:
            set_list += f"""
                <set>
                  <strategy>merge</strategy>
                  <path>thoughtChainItems[{index}].status</path>
                  <value>{status}</value>
                </set>
            """
        return f"""
              <message-embedded>
              <state>
                {set_list}
              </state>
            </message-embedded>
        """
        
    def finish_node(self, index, status: str = "success"):
        return f"""
          <message-embedded>
            <state>
              <set>
                <strategy>replace</strategy>
                <path>thoughtChainItems[{index}].status</path>
                <value>{status}</value>
              </set>
            </state>
          </message-embedded>
        """
        
