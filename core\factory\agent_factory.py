from typing import Dict, Any
from core.config.app_logger import logger
from core.services.database.crud.ai_agent_dao import ai_agent_crud
from core.base.langchain_sender import LangchainBaseSender  
from core.base.dify_sender import DifyBaseSender
from importlib import import_module
# Agent工厂类类，用于动态加载agent
# @Author: xiangjh

class AgentFactory:
    """Agent工厂中枢
    
    实现以下核心功能：
    - 代理元数据动态加载
    - 多目标平台适配器注册
    - 运行时对象缓存管理
    - 异常安全机制
    - 服务发现 & 路由控制
    """
    _senders: Dict[str, Any] = {}
    _agents: Dict[str, Any] = {}

    @classmethod
    def dynamic_register_agents(cls):
       from core.config.app_config import config    
       """ 动态注册配置文件中定义的agent实现 """
       for  agent_cfg in config.agents:
            if not agent_cfg.impl:
                continue
                
            try:
                # 动态导入类
                module_path, class_name = agent_cfg.impl.rsplit('.', 1)
                module = import_module(module_path)
                sender_class = getattr(module, class_name)
                
                # 实例化并注册
                sender = sender_class(agent_cfg.name)
                cls.register_sender(agent_cfg.name, sender)
                logger.info(f"动态注册 sender: {agent_cfg.name} => {agent_cfg.impl}")
                
            except Exception as e:
                logger.error(f"动态注册 sender失败 [{agent_cfg.name}]: {str(e)}", exc_info=True)

    @classmethod
    async def load_agents(cls):
        """数据库元数据加载器
        
        执行流程：
        1. 从持久化存储加载代理元数据
        2. 动态注册目标平台适配器
        3. 构建代理对象缓存
        4. 异常代理自动熔断

        Raises:
            DatabaseError: 数据库查询失败时抛出
            ValidationError: 元数据校验失败时抛出
        """
        try:
            cls.dynamic_register_agents()
            agents = await ai_agent_crud.query_agents()
            agent_mapping = {}
            for agent in agents:
                try:
                    if sender := cls._senders.get(agent.agent_code):
                        logger.debug(f"使用预注册的发送器: {agent.agent_code}")
                    else:
                        sender_class = cls._get_sender_by_target(agent.target_type)    
                        if isinstance(sender_class, type):  # 如果是类
                            sender = sender_class(agent.agent_code)  # 实例化
                        else:  # 函数式发送器
                            sender = sender_class

                    agent_mapping[agent.agent_code] = sender
                    cls._agents[agent.agent_code] = agent
                    logger.info(f"成功注册 agent: {agent.agent_name}, target: {agent.target_type}")
                except ValueError as e:
                    logger.warning(f"跳过 agent '{agent.agent_name}': {str(e)}")

            cls.register_senders(agent_mapping)
        except Exception as e:
            logger.error(f"加载 agents 失败: {str(e)}")
            raise

    @classmethod
    def get_sender(cls, agent_code: str) -> Any:
        """适配器路由控制器
        Args:
            agent_code (str): 代理唯一标识符，格式：<业务域>.<功能模块>

        Returns:
            Any: 已注册的目标平台适配器实例

        Raises:
            ServiceNotFound: 当适配器未注册时抛出
        """
        sender = cls._senders.get(agent_code)
        if not sender:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的 sender")
        return sender

    @classmethod
    def get_agent(cls, agent_code: str) -> Any:
        """
        从_agents中获取指定的agent对象
        
        :param agent_code: Agent的唯一标识符
        :return: 对应的agent对象
        :raises ValueError: 如果未找到对应的agent
        """
        agent = cls._agents.get(agent_code)
        if not agent:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的agent")
        return agent

    @classmethod
    def register_sender(cls, agent_code: str, sender: Any):
        cls._senders[agent_code] = sender

    @classmethod
    def register_senders(cls, senders: Dict[str, Any]):
        cls._senders.update(senders)

    @classmethod
    def _get_sender_by_target(cls, target: str):
        """目标平台适配器解析器
        
        支持平台类型：
        - dify: Dify 智能体平台
        - langchain: LangChain 执行引擎

        Args:
            target (str): 目标平台标识符

        Returns:
            Callable: 平台专用适配方法

        Raises:
            UnsupportedTarget: 当目标平台不受支持时抛出
        """
        if target == "dify":
            return DifyBaseSender
        elif target == "langchain":
            return  LangchainBaseSender
        else:
            raise ValueError(f"未知的 target 类型: {target}")
    


