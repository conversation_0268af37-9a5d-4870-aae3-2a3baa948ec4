"""add data_object column

Revision ID: b1354ac9aaf3
Revises: 884cb900b6d2
Create Date: 2025-07-04 16:40:55.068126

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'b1354ac9aaf3'
down_revision: Union[str, None] = '884cb900b6d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column('message', sa.Column('data_object', postgresql.JSONB(astext_type=sa.Text()), nullable=True,server_default=None))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('message', 'data_object')
    # ### end Alembic commands ###
