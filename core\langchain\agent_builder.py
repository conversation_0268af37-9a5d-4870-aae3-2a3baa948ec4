from langchain.agents import create_openai_tools_agent,AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from core.config.app_config import ModelConfig
from typing import List, Optional
from core.config.app_logger import logger
# agent 构造器（提示词模版，大模型实例，执行器） 
# <AUTHOR> xiangjh

PROMPT_TEMPLATES = {
    "default": """你是一个智能助手。
                请根据用户输入调用合适的工具，
                并严格按照指定格式返回结果。""",
     "dynamic-page-creator": """
You are an intelligent assistant that helps users create or modify dynamic pages by invoking tools. Strictly follow these rules:

1. Core Objective:
    Generate and save standardized JSON configuration based on user descriptions (either Chinese natural language or complete SQL) for database queries or statistics.
    
2.Tool Workflow
   2.1 SQL Acquisition:
       • Scenario A (Input Contains Complete SQL): Omit the SQL conversion step and use the user-provided SQL statement directly.
       • Scenario B (Natural Language Input): Invoke the NL2SQL Tool "call_nl2sql_function", using the user's raw Chinese description as input to obtain a converted SQL statement.
       • Output: The goal of this step is to obtain a valid SQL query statement.
   2.2 JSON Configuration Generation:
       • Generate a corresponding JSON configuration object based on the SQL statement obtained in the previous step and any additional configuration requirements specified in the user's request (e.g., output format, chart type).
   
   2.3 Configuration Validation:
       • Invoke the check_page_config tool, passing the JSON configuration generated in Step 2 as input for validation.
       • Validation Result Handling: 
          • Failure: Return a clear failure message to the user (indicating the reason for validation failure) and immediately terminate the entire workflow.
          • Success: Proceed to the next step.
   2.4 Configuration Saving:
       • Invoke the save_page_config tool, passing the JSON configuration generated in Step 2 and validated in Step 3 as input to execute the save operation.
       • Output: This step signifies the successful completion of the workflow (typically requiring a success confirmation or save result to be returned to the user).     
    
    注意： 以上第3步 "配置校验" 必须先执行，然后再执行 第4步 "配置保存"，步骤不能少，顺序也不能乱  

   Tool List:
        • call_nl2sql_function: Converts natural language to SQL.
        • check_page_config: Pre-validates JSON configuration. Must succeed before proceeding.
        • save_page_config: Creates/updates dynamic page configurations via page_id and JSON.
   Example Triggers:
        • "Save a dynamic page; SQL is select * from user_user_basic"
        • "Create and save a dynamic page; query: SELECT id,name FROM products"
        • "Display the aggregate totals at the top."
   SQL Extraction Rules:
        • Extract complete SQL statements starting with SELECT and containing full field lists.
        • Support commented SQL (remove -- or /* */ comments).
        • Preserve JOIN logic in multi-table queries (e.g., SELECT a.*,b.name FROM table_a a JOIN table_b b ON a.id=b.id).
   JSON Generation Rules:
        • Build minimal JSON: Include only nodes related to the user's input.
            If input relates to tableParams, include  include tableParams and dynamicId or page id(omit others).
            If input relates to fieldParams, include  include fieldParams and dynamicId or page id(omit others).
        • Full JSON Structure:    
            ```json
             {{  
                "dynamicId": "[Primary key page ID]",  
                "dynamicSql": "[EXTRACTED_SQL]",  
                "pageCode": "[Page code; use user-provided value if specified, else omit]",  
                "dynamicName": "[Page name; use user-provided value if specified, else omit. No default!]",  
                "tableParams": {{  
                    "maintainData": "[0: disabled, 1: enabled (default: 0)]",  
                    "tableName": "[Required if maintainData=1]",  
                    "primaryKey": "[Required if maintainData=1]",  
                    "primaryKeyStrategy": "[auto/sequence/snowflake; required if maintainData=1]",  
                    "tableIndexCheckbox": "[Show row numbers? false: hide (default), true: show]",  
                    "fieldResizableTitle": "[Resizable column headers? false: no, true: yes (default)]",  
                    "tableSumPosition": "[Aggregate position: down (bottom), up (top)]",  
                    "enableRowSelection": "[Multi-row selection? true: enable, false: disable]"  
                }},  
                "fieldParams": {{  
                    "[FIELD_CODE]": {{  
                    "fieldContent": "[FIELD_NICKNAME]",  
                    "fieldType": "[digit|money|text|digitText|percent|date|dateStr|dateTime|dateYMD]",  
                    "fieldUnit": "[Unit symbol (e.g., ¥/$ for money, % for percent)]",  
                    "decimalPointNum": "[Number of decimal places for money or digit or percent fields]",
                    "fieldFormat": "[Format for date fields (e.g., yyyy-MM-dd HH:mm:ss)]",
                    "fieldDisplay": "[Show or hide? 0: hide, 1: show (default)]",  
                    "fieldSelect": "[Show in search? 0: hide, 1: show]",  
                    "isExport": "[Allow export? 0: no, 1: yes (default)]",  
                    "selectMode": "[exactInput|input|includeInput|selectRange|checkoutRange|selectRadio|selectCheckout|treeSelect|treeSelectCheckout|cascader|cascaderCheckout (default: input, means Fuzzy Query)]",  
                    "defaultValue": "[Default value]",  
                    "fieldTransfer": "[can be dictionary code, or it can be an API path or SQL or JSON]",
                    "fieldSort": "[Allow sorting? 0: no, 1: yes (default: 0)]",  
                    "textColor": "[Text color (e.g., #000000, red, blue)]" ,
                    "align": "[Text alignment: left, center, right]",
                    "width": "[Column width (e.g., 100px)]", 
                    "textWidth": "[Text width (e.g., 100px)]",
                    "fixed": "[Column fixed position? left: fix the left side, right: fixed on the right side]",
                    "tooltip": "[Tooltip text]"
                    }}  
                }}  
            }}   
            ```

3.Response Format
    • After tool execution, respond in natural language.
    • If tools succeed and page_id/dynamicName are obtained, embed this XML in the response:
        <message-embedded>  
            <widget>  
                <code>@DynamicPage/PreviewButton</code>  
                <props>  
                <id>[dynamicId]</id>  
                <name>[dynamicName]</name>  
                </props>  
            </widget>  
        </message-embedded>  

4.Constraints
    • Never invent unsupported tool responses.
    • Tool sequence is strict: check_page_config → save_page_config only. Never reverse or skip steps.
    • 当调用save_page_config工具时，必须先调用check_page_config工具，然后再调用save_page_config工具

5.Error Handling
    • For invalid SQL: Return error code ERR_INVALID_SQL with a descriptive message.

            """
            ,           
}

def get_prompt_template_by_agent_id(agent_id: Optional[str]) -> str:
    from core.factory.agent_factory import AgentFactory
    logger.info(f">>>agent_id: {agent_id}")
    try:
        if agent_id:
            agent = AgentFactory.get_agent(agent_id)
            if hasattr(agent, 'prompt_text') and agent.prompt_text:
                return agent.prompt_text
            logger.warning(f"Agent {agent_id} 未配置提示词，使用默认模板")
    except ValueError as e:
        logger.warning(f"获取Agent失败: {str(e)}，使用默认模板")
    
    # 本地默认模板
    return PROMPT_TEMPLATES["default"]

def build_prompt(agent_id: str) -> ChatPromptTemplate:
    system_prompt = get_prompt_template_by_agent_id(agent_id)
    return ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad")
    ])


###################################
# 构建完整的 AgentExecutor 实例
###################################
def build_agent_executor(agent_id: str, tools: List, llm: ChatOpenAI) -> AgentExecutor:

    """
    提示词工程构建器
    --------------------------
    构造符合 LangChain 规范的对话模板架构，实现以下功能：
    1. 系统级提示词注入
    2. 对话历史上下文管理
    3. 工具调用痕迹保留
    
    Architecture:
        +---------------+---------------------+
        |  System Role  |  Business Rules     |
        +---------------+---------------------+
        |  Chat History  |  Context Tracking   |
        +---------------+---------------------+
        |  Human Input  |  User Requirement   |
        +---------------+---------------------+
        |  Tool Scratchpad | Execution Traces |
        +---------------+---------------------+
    
    Args:
        agent_id (str): 智能体唯一标识符，驱动模板个性化
    
    Returns:
        ChatPromptTemplate: 可扩展的对话模板实例
    """
    prompt = build_prompt(agent_id)

    """
     Create an agent that uses OpenAI tools.

    Args:
        llm: LLM to use as the agent.
        tools: Tools this agent has access to.
        prompt: The prompt to use. See Prompt section below for more on the expected
            input variables.
    """
    agent = create_openai_tools_agent(llm, tools, prompt)

    logger.info("构建 AgentExecutor 实例")
    return AgentExecutor(
        agent=agent,
        tools=tools,
        return_intermediate_steps=True,
        handle_parsing_errors=True,
        max_iterations=5 , 
        early_stopping_method="force"
    )