"""
手动测试软删除功能的简单脚本
"""
import asyncio
from datetime import datetime, timezone

from core.services.database.schemas.conversation import (
    ConversationCreate,
)
from core.services.database.crud.conversation import conversation_curd
from core.services.database import db_manager


async def test_soft_delete_functionality():
    """手动测试软删除功能"""
    print("开始测试软删除功能...")
    
    try:
        async with db_manager.session() as session:
            # 1. 创建测试会话
            print("1. 创建测试会话...")
            conversation_data = ConversationCreate(
                title="软删除测试会话",
                user_id=999,  # 使用特殊的测试用户ID
                current_agent_code="test_agent",
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )
            conversation = await conversation_curd.create(db=session, obj_input=conversation_data)
            print(f"   创建成功，会话ID: {conversation.id}")
            print(f"   is_deleted: {conversation.is_deleted}")

            # 2. 验证会话可以正常查询
            print("\n2. 验证会话可以正常查询...")
            found_conversation = await conversation_curd.get(db=session, _id=conversation.id)
            assert found_conversation is not None
            print("   ✓ 会话可以正常查询")

            # 3. 执行软删除
            print("\n3. 执行软删除...")
            deleted_conversation = await conversation_curd.soft_delete(db=session, _id=conversation.id)
            assert deleted_conversation is not None
            assert deleted_conversation.is_deleted is True
            print("   ✓ 软删除成功")

            # 4. 验证正常查询无法获取已删除的会话
            print("\n4. 验证正常查询无法获取已删除的会话...")
            found_conversation = await conversation_curd.get(db=session, _id=conversation.id)
            assert found_conversation is None
            print("   ✓ 正常查询无法获取已删除的会话")

            # 5. 验证包含已删除记录的查询可以获取会话
            print("\n5. 验证包含已删除记录的查询可以获取会话...")
            found_conversation = await conversation_curd.get(db=session, _id=conversation.id, include_deleted=True)
            assert found_conversation is not None
            assert found_conversation.is_deleted is True
            print("   ✓ 包含已删除记录的查询可以获取会话")

            # 6. 测试恢复功能
            print("\n6. 测试恢复功能...")
            restored_conversation = await conversation_curd.restore(db=session, _id=conversation.id)
            assert restored_conversation is not None
            assert restored_conversation.is_deleted is False
            print("   ✓ 恢复成功")

            # 7. 验证恢复后可以正常查询
            print("\n7. 验证恢复后可以正常查询...")
            found_conversation = await conversation_curd.get(db=session, _id=conversation.id)
            assert found_conversation is not None
            assert found_conversation.is_deleted is False
            print("   ✓ 恢复后可以正常查询")

            # 8. 测试按用户ID查询时排除已删除的会话
            print("\n8. 测试按用户ID查询时排除已删除的会话...")
            # 再次软删除
            await conversation_curd.soft_delete(db=session, _id=conversation.id)
            
            # 查询用户的会话列表
            user_conversations = await conversation_curd.get_by_user_id(db=session, user_id=999)
            conversation_ids = [c.id for c in user_conversations]
            assert conversation.id not in conversation_ids
            print("   ✓ 按用户ID查询时正确排除已删除的会话")

            # 9. 测试获取已删除的会话列表
            print("\n9. 测试获取已删除的会话列表...")
            deleted_conversations = await conversation_curd.get_deleted_conversations(db=session, user_id=999)
            deleted_ids = [c.id for c in deleted_conversations]
            assert conversation.id in deleted_ids
            print("   ✓ 可以正确获取已删除的会话列表")

            # 10. 清理：硬删除测试数据
            print("\n10. 清理测试数据...")
            await conversation_curd.remove(db=session, _id=conversation.id, hard_delete=True)
            
            # 验证硬删除后完全不存在
            found_conversation = await conversation_curd.get(db=session, _id=conversation.id, include_deleted=True)
            assert found_conversation is None
            print("   ✓ 硬删除成功，测试数据已清理")

        print("\n🎉 所有软删除功能测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_soft_delete_functionality())
