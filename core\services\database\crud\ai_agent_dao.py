from typing import List
from sqlmodel import select

from core.services.database import db_manager
from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.ai_agent import AIAgentTable, AIAgentCreate, AIAgentUpdate


class CRUDAIAgent(CRUDBase[AIAgentTable, AIAgentCreate, AIAgentUpdate]):
    """AI Agent CRUD操作实现"""

    async def query_agents(self) -> List[AIAgentTable]:
        """获取所有AI Agent列表"""
        async with db_manager.session() as db:
            result = await db.execute(select(AIAgentTable))
            return result.scalars().all()


ai_agent_crud = CRUDAIAgent(AIAgentTable)