from core.config.app_config import config
from typing import Optional, List, Dict, Any, AsyncGenerator, Union
from langchain_core.tools import tool
import httpx
import json
from utils.common_utils import CommonUtils
safe_json_dumps = CommonUtils.safe_json_dumps
from core.config.app_logger import logger

class DifyClient:
    def __init__(
        self,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
    ):
        self.model_config = config.model
        self.model_name = model_name or self.model_config.name
        self.api_key = api_key or self.model_config.api_key
        self.user_id = user_id
        self.agent_id = agent_id
        
        if not self.model_name:
            raise ValueError("model_name 必须指定或在配置中定义")

    @staticmethod 
    def process_chunk_for_dify(chunk: str) -> Optional[str]:
        """
        从 DIFY 的流式响应中提取 data.text 内容。
        
        :param chunk: 原始 chunk 数据，例如：'data: {"event": "text_chunk", "data": {"text": "你好"}}'
        :return: 提取出的文本内容，如 "你好" 或 None（解析失败时）
        """
        logger.info(f"[process_chunk_for_dify] 收到原始 chunk 数据: {chunk}")
        lines = chunk.strip().split('\\n')
        for line in lines:
            logger.info(f"line: {line}")
            if not line.startswith("data: "):
                continue

            try:
                json_str = line[6:]
                
                data = json.loads(json_str)
                if data.get("event") == "text_chunk":
                    return data.get("data", {}).get("text")

            except json.JSONDecodeError as e:
                logger.error(f"JSON 解析失败: {e}, line 内容: {line}")

        return None

 

    
    #####################################################################################################################
    # 大模型流式调用入口，返回按事件处理后的数据
    # {"type": "stream", "content": "上海"}
    # {"type": "stream", "content": "当前"}
    # {"type": "stream", "content": "天气"}
    # {"type": "stream", "content": "25"}
    # {"type": "stream", "content": "度"}
    # 
    #####################################################################################################################
    async def chat_stream(self, question: str, session_id: str = "default") -> AsyncGenerator[Union[str, dict], None]:
        try:
            # 从配置中获取当前Agent的配置
            agent_code = self.agent_id
            
            from core.factory.agent_factory import AgentFactory
            agent = AgentFactory.get_agent(self.agent_id)

            if not agent:
                raise ValueError(f"未找到名为 {agent_code} 的Agent配置")
            # 使用配置中的URL和API Key
            url = agent.base_url
            api_key = f"Bearer {agent.api_key}"

            headers = {
                "Authorization": api_key,
                "Accept": "text/event-stream",
                "Content-Type": "application/json",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
            payload = {
                "inputs": {"message": question},
                "response_mode": "streaming",
                "user": "elephant001"
            }

            async with httpx.AsyncClient(timeout=60 * 3) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    if response.status_code != 200:
                        yield safe_json_dumps({"type": "error", "message": f"请求失败，状态码: {response.status_code}"})
                        return

                    async for raw_line in response.aiter_bytes():
                        if not raw_line:
                            continue

                        line = raw_line.decode("utf-8", errors="ignore").strip()

                        if "node_finished" in line:  # 跳过工作流节点完成事件
                            logger.debug(f"跳过 node_finished 事件: {line[:80]}...")
                            continue

                        if line.startswith("data: "):
                            
                            json_str = line[6:]
                            try:
                                data = json.loads(json_str)
                                if data.get("event") == "text_chunk":
                                    yield safe_json_dumps({
                                        "type": "stream",
                                        "content": data["data"]["text"]
                                    })
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON 解析失败: {e}, 内容: {json_str}")

        except Exception as e:
            logger.error(f"流式处理异常: {str(e)}", exc_info=True)
            yield safe_json_dumps({
                "type": "error",
                "message": f"对话中断: {str(e)}"
            })

        finally:
            logger.info("流式响应结束")
        