from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import JSONResponse
import httpx  # 异步 HTTP 客户端
from core.config.security import check_user
from core.config.app_config import config

router = APIRouter(
    prefix="/gateway", tags=["Gateway"], dependencies=[Depends(check_user)]
)

# 模拟一个简单的鉴权逻辑
async def authenticate(request: Request):
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise HTTPException(status_code=401, detail="Authorization header is missing")

    # 假设token校验成功
    # TODO: Replace with actual token validation logic (e.g., JWT verification)
    if "Bearer valid_token" not in auth_header:
        raise HTTPException(status_code=401, detail="Invalid token")

    # 在request state中添加user信息，传递给下游服务
    request.state.user = {"user_id": "123", "roles": ["user"]}
    return True  # Authentication successful


# 封装请求转发函数
async def forward_request(request: Request, target_url: str, path: str):
    """Forward incoming request to the specified target URL."""
    try:
        # 复制headers
        headers = dict(request.headers)

        # 移除host header，防止目标服务报错
        headers.pop("host", None)

        # 添加用户信息到下游请求头
        # if hasattr(request.state, "user"):
        #     headers["X-User-Id"] = request.state.user["user_id"]
        #     headers["X-User-Roles"] = json.dumps(request.state.user["roles"])

        async with httpx.AsyncClient(follow_redirects=True) as client:
            # 读取请求体
            content = None
            if request.method == "POST" or request.method == "PUT":
                content = await request.body()

            # 发送请求
            response = await client.request(
                method=request.method,
                url=target_url + "/" + path,  # 构造目标URL
                headers=headers,
                content=content,
                params=request.query_params,  # 传递query parameters
            )

        content = response.json()
        return JSONResponse(
            content=content,
            status_code=response.status_code,
        )

    except httpx.RequestError as e:
        print(f"Request Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {e}"
        ) from e
    except httpx.HTTPStatusError as e:
        print(f"HTTP Status Error: {e}")
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.text
        ) from e
    except Exception as e:
        print(f"Other Error: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {e}"
        ) from e


# 捕获所有路径
@router.api_route(
    "/{service_name}/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    include_in_schema=False,
)
async def gateway(
    request: Request, service_name: str, path: str
):  # path:str可以用来访问捕获的路径
    """Main gateway function to route requests to microservices."""

    # 1. 鉴权
    # try:
    #     await authenticate(request)
    # except HTTPException as e:
    #     return JSONResponse(content={"detail": e.detail}, status_code=e.status_code)
    services = config.full_config.get("gateway").get("services")

    # 2. 路由
    target_url = None
    for service in services:
        if service.get("name") == service_name:
            target_url = service.get("url")
            break
    if not target_url:
        raise HTTPException(status_code=404, detail="Endpoint not found")
    if target_url.startswith("http://") or target_url.startswith("https://"):
        pass
    else:
        target_url = f"http://{target_url}"

    # 3. 请求转发
    return await forward_request(request, target_url, path)
