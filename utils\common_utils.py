# -*- 通用工具类 -*-
# 将 MCP 工具的输入 schema 转换为 OpenAI Function Calling 所需的格式，并删除多余字段
# <AUTHOR> : 2025-04-27 15:33
from typing import List, Dict, Any, Optional
from datetime import datetime
from typing import AsyncIterator, <PERSON><PERSON>, Any
import json
from core.config.app_logger import logger
class CommonUtils:
    def __init__(self):
        pass
    def is_empty(self, value):
        return value is None or value == "" or value == [] or value == {} or value == () or value == set() or value == frozenset() or value == 0 or value == False or value == 0.0 or value == 0j or value == b"" or value == bytearray() or value == memoryview(b"")
    
    async def transform_json(self, json_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]: 
        """
        将工具的 input_schema 转换为 OpenAI 所需的 parameters 格式，并删除多余字段
        """
        result = []
        for item in json_data:
            if not isinstance(item, dict) or "type" not in item or "function" not in item:
                continue
            old_func = item["function"]
            if not isinstance(old_func, dict) or "name" not in old_func or "description" not in old_func:
                continue
            new_func = {
                "name": old_func["name"],
                "description": old_func["description"],
                "parameters": {}
            }
            if "input_schema" in old_func and isinstance(old_func["input_schema"], dict):
                old_schema = old_func["input_schema"]
                new_func["parameters"]["type"] = old_schema.get("type", "object")
                new_func["parameters"]["properties"] = old_schema.get("properties", {})
                new_func["parameters"]["required"] = old_schema.get("required", [])
                new_item = {
                    "type": item["type"],
                    "function": new_func
                }
            result.append(new_item)
        return result
    


    @staticmethod
    def generate_session_id(user_id: str, agent_code: str, conversation_id: str) -> str:
        if not conversation_id:
            conversation_id = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"user:{user_id}:agentcode:{agent_code}:{conversation_id}"
    
    from typing import AsyncIterator, Tuple, Any

    @staticmethod
    def safe_json_dumps(data: Any, ensure_ascii: bool = False, indent: Optional[int] = None) -> str:
        """
        安全地将对象序列化为 JSON 字符串，失败时返回空对象字符串 '{}'

        :param data: 要序列化的对象
        :param ensure_ascii: 是否确保 ASCII 编码，默认为 False
        :param indent: 缩进空格数，用于美化输出，默认为 None
        :return: JSON 字符串
        """
        try:
            return json.dumps(data, ensure_ascii=ensure_ascii, indent=indent)
        except (TypeError, ValueError) as e:
            logger.error(f"JSON 序列化失败: {e}", exc_info=True)
            return '{}'

# 异步流式处理工具，包装原始 stream，每个元素返回 (item, is_last) 形式
async def stream_with_last_(stream: AsyncIterator[Any]) -> AsyncIterator[Tuple[Any, bool]]:
    """
    异步流式处理工具，包装原始 stream，
    每个元素返回 (item, is_last) 形式。
    
    示例：
        async for item, is_last in with_last(stream):
            ...
    """
    it = stream.__aiter__()
    last_val = await it.__anext__()  # 获取第一个值
    async for val in it:
        yield (last_val, False)
        last_val = val
    yield (last_val, True)

