from core.langchain.redis_with_postgres import RedisWithPostgreSQLHistory
from langchain_core.messages import ToolMessage
from core.config.app_config import config

# 历史记录管理类
# <AUTHOR> xiangjh

class HistoryManager:
    def __init__(self, user_id=None, agent_id=None):
        """初始化历史管理器
            Args:
                user_id (str): 用户全局唯一标识，用于跨会话追踪
                agent_id (str): 智能体标识，用于区分不同业务场景
        """
        self.user_id = user_id
        self.agent_id = agent_id

    def get_redis_history(self, session_id: str) -> RedisWithPostgreSQLHistory:
        """构建混合存储历史实例
        基于应用配置构建 Redis+PostgreSQL 双存储引擎，实现：
        1. Redis 高频访问数据缓存
        2. PostgreSQL 持久化存储
        3. TTL 自动过期管理

        Args:
            session_id (str): 会话唯一标识，格式：user_id:agent_id:conversation_id

        Returns:
            RedisWithPostgreSQLHistory: 预配置的混合存储实例
        """
        redis_config = config.redis
        host = redis_config.host
        port = redis_config.port
        password = redis_config.password
        db = redis_config.db

        redis_url = f"redis://{host}:{port}/{db}" if not password else f"redis://:{password}@{host}:{port}/{db}"

        return RedisWithPostgreSQLHistory(
            session_id=session_id,
            user_id=self.user_id,
            agent_id=self.agent_id,
            url=redis_url,
            ttl=604800
        )

    async def load_chat_history(self, session_id: str):
        history = self.get_redis_history(session_id)
        raw_messages = await history.aget_messages()
        return [msg for msg in raw_messages if not isinstance(msg, ToolMessage)], history