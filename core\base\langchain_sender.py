from typing import Dict, Any, Optional,AsyncIterator
import json
from fastapi import Request
from llmclient.llm_client import LLMClient
from core.config.app_logger import logger
from utils.common_utils import stream_with_last_
from core.base.base_sender import BaseSender
import re
from core.config.app_constant import AppConstant

# LangChain 发送器基类，用于封装流式响应生成逻辑。 
# <AUTHOR> xiangjh
class LangchainBaseSender(BaseSender):
    """
    提供统一的接口来处理与 LangChain 相关的消息发送流程，
    支持子类继承和扩展以实现自定义行为。
    
    Attributes:
        agent_code (str): 当前 Agent 的唯一标识符。
        _llm_client (LLMClient): LLM 客户端实例，延迟初始化。
    """
    def __init__(self, agent_code: str):
        """
          初始化 LangchainBaseSender 实例。
          
          Args:
             agent_code (str): 当前 Agent 的唯一标识符，用于会话 ID 生成及日志记录。
        """
        super().__init__(agent_code)
        self._llm_client = None 

    async def _generate_content(self, 
                            request: Request,
                            question: str,
                            user_id: str,
                            conversation_id: str,
                            extend_params: dict) -> AsyncIterator[Any]:
        """
        异步生成流式响应数据块，并通过 Transmitter 发送。
        
        流程包含：
        - 初始化 LLM 客户端
        - 构建最终问题
        - 处理 chunk 数据并发送
        - 错误处理及回退机制
        
        Args:
            request (Request): FastAPI 请求对象，用于获取上下文信息。
            question (str): 用户输入的问题文本。
            user_id (str): 用户唯一标识符。
            conversation_id (str): 会话唯一标识符。
            
        Yields:
            AsyncIterator[Any]: 异步生成的消息数据块（如字符串或字典）。
        """
        try:

            llm_client = await self._get_llm_client(request, user_id)
            
            final_question = await self._build_final_question(question)
            logger.info(f"原始问题：{final_question}")
            session_id = self._generate_session_id(user_id, conversation_id)
            
            async for chunk, is_last in stream_with_last_(llm_client.chat_stream(question=final_question, session_id=session_id)):
                if isinstance(chunk, str):
                    chunk = json.loads(chunk)

                async for processed_data in self._process_chunk(chunk):
                    if processed_data:
                        yield {
                            "data": processed_data["data"],
                            "is_last": processed_data.get("is_last", is_last),
                            "is_new_package": processed_data.get("is_new_package") ,
                            "package_type": processed_data.get("package_type", AppConstant.DEFAULT_PACKAGE_TYPE)  
                        }
                    
        except Exception as e:
            error_msg = f"服务异常，请稍后再试: {str(e)}"
            logger.error(error_msg, exc_info=True)  
            yield self._wrap_package(error_msg)  
            return

    async def _build_final_question(self, raw_question: str) -> str:
        """问题构建（可重写）"""
        logger.info(f"原始问题：{raw_question}")
        return raw_question

    async def _process_chunk(self, chunk: Dict[str, Any]) -> AsyncIterator[dict]:
        """默认块处理器"""
        chunk_type = chunk.get("type")
        package_type = AppConstant.DEFAULT_PACKAGE_TYPE
        if chunk_type == "stream":
            yield self._handle_stream_conten(chunk)
        elif chunk_type == "tool_start":
            result = self._handle_tool_start(chunk)
            yield self._ensure_package(result, default_type=package_type)
        elif chunk_type == "tool_result":
            result = self._handle_tool_result(chunk)
            yield self._ensure_package(result, default_type=package_type)
        elif chunk_type == "response":
            async for result in self._handle_response(chunk):
                yield self._ensure_package(result)
        elif chunk_type == "error":
            yield chunk.get("message", "")



    def _ensure_package(self, result, default_type=0):
        if isinstance(result, dict) and 'package_type' in result:
            return result
        return self._wrap_package(result, default_type)

    def _wrap_package(self, data, package_type):
        """包装数据包结构"""
        return {
            "data": data,
            "package_type": package_type
        }    
    
    def _handle_stream_conten(self, chunk: Dict[str, Any]) -> dict:
        return self._wrap_package(chunk.get("content"), AppConstant.DEFAULT_PACKAGE_TYPE)

    def _handle_tool_start(self, chunk: Dict[str, Any]) -> dict:
        """
        处理工具调用结果类型的 chunk （可单独重写的子方法）。
        
        将原始输出解析为结构化数据，并生成对应的 XML 格式响应。
        {"type": "tool_start", "tool": "call_nl2sql_function",
          "input": {"kwargs": {"query": "查询所有企业2020年所有财务报表关于营业收入，净利润，应收账款 三个数据"}}}
        <state>
            <set>
                <strategy>replace</strategy>
                <path>tool1Status</path>
                <value>loading</value>
            </set>
        </state>

        Args:
            chunk (Dict[str, Any]): 包含 tool 和 output 的数据块。
            
        Returns:
            str: XML 格式的工具调用结果表示。
        """
        tool_name = chunk.get("tool")
        if not tool_name:
            return None
        status= "loading"
        tool_result = ""
        
        xml_data = f"""<message-embedded>
                            <state>
                                <set>
                                    <strategy>replace</strategy>
                                    <path>{tool_name}</path>
                                    <value>{status}</value>
                                </set>
                                <set>
                                    <strategy>replace</strategy>
                                    <path>{tool_name}_result</path>
                                    <value>{tool_result}</value>
                                </set>
                            </state>
                            <widget>
                                <code>@BuildIn/Tool</code>
                                <props>
                                    <name>{tool_name}</name>
                                    <status>{{{{state.{tool_name}}}}}</status>
                                    <result>{{{{state.{tool_name}_result}}}}</result>
                                </props>
                            </widget>
                        </message-embedded>"""
        return self._wrap_package(xml_data, AppConstant.DEFAULT_PACKAGE_TYPE)

    
    
    def _handle_tool_result(self, chunk: Dict[str, Any]) -> dict:
        """
        处理工具调用结果类型的 chunk （可单独重写的子方法）。
        
        将原始输出解析为结构化数据，并生成对应的 XML 格式响应。
        
        Args:
            chunk (Dict[str, Any]): 包含 tool 和 output 的数据块。
            
        Returns:
            str: XML 格式的工具调用结果表示。
        """
        tool_name = chunk.get("tool")
        if not tool_name:
            return None
        
        tool_result = chunk.get("result", "")
        tool_msg = chunk.get("tool_msg", "")
        raw_output = chunk.get("output", "")
        
        status= "failed"
        logger.warning(f"tool_result: {tool_result}")
        if tool_result :
            status = "success" if tool_result == "success" else "failed"
        else:
            if isinstance(raw_output, str):
                text_match = re.search(r"text='({.*?})'", raw_output, re.DOTALL)
                if text_match:
                    try:
                        # 处理多层转义字符
                        text_content = (
                            text_match.group(1)
                            .replace("\\'", "'")
                            .replace('\\\\"', '"')
                            .replace('\\"', '"')
                        )
                        content_data = json.loads(text_content)
                        success = content_data.get('success', False)
                        status = "success" if success else "failed"
                        
                    except Exception as e:
                        logger.error(f"最终解析失败: {str(e)}")
                        status = "failed"
        
        xml_data = f"""<message-embedded>
                            <state>
                                <set>
                                    <strategy>replace</strategy>
                                    <path>{tool_name}</path>
                                    <value>{status}</value>
                                </set>
                                <set>
                                    <strategy>replace</strategy>
                                    <path>{tool_name}_result</path>
                                    <value>{tool_msg}</value>
                                </set>
                            </state>
                        </message-embedded>"""
        return self._wrap_package(xml_data, AppConstant.DEFAULT_PACKAGE_TYPE)

    def _generate_preview_widget(self, dynamic_id: str, dynamic_name: str) -> str:
        """
        生成预览按钮的 XML 格式 widget 字符串
        """
        return f"""<message-embedded>
        <widget>
            <code>@DynamicPage/PreviewButton</code>
            <props>
                <id>{dynamic_id}</id>
                <name>{dynamic_name}</name>
            </props>
        </widget>
    </message-embedded>"""
   
    async def _get_llm_client(self, request: Request, user_id: str) -> LLMClient:
        """
        获取或初始化 LLM 客户端实例 （子类可重写）。
        
        若客户端尚未初始化，则使用当前请求和用户信息创建新实例。
        
        Args:
            request (Request): FastAPI 请求对象。
            user_id (str): 用户唯一标识符。
            
        Returns:
            LLMClient: 已初始化的 LLM 客户端实例。
        """
        if not self._llm_client:
            self._llm_client = LLMClient(
                request=request,
                user_id=user_id,
                agent_id=self.agent_code
            )
            await self._llm_client.init_async()
        return self._llm_client