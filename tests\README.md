# 测试说明

本目录包含项目的测试用例。

## 运行测试

在项目根目录下运行以下命令来执行所有测试：

```bash
python -m unittest discover -s tests
```

运行特定测试文件：

```bash
python -m unittest tests/test_transmitter.py
```

运行特定测试类：

```bash
python -m unittest tests.core.message.test_transmitter.TestTransmitter
```

运行特定测试方法：

```bash
python -m unittest tests.core.message.test_transmitter.TestTransmitter.test_init
```

## 测试结构

- `tests/core/message/test_transmitter.py`: Transmitter 类的测试用例
