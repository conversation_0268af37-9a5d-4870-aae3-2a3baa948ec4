from .util import create_message_chunks

MESSAGE = """
工具调用

<message-embedded>
    <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>查询工具调用</name>
            <result>工具输出结果xxxxx</result>
            <success>0</success>
        </props>
    </widget>
    <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>查询工具调用</name>
            <result>工具输出结果xxxxx</result>
            <success>1</success>
        </props>
    </widget>
</message-embedded>

"""

def create_tool_message_chunks():
    return create_message_chunks(MESSAGE)
