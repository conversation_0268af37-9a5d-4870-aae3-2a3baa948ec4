from contextlib import AsyncExitStack
from typing import Optional
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from mcp.client.streamable_http import streamablehttp_client
from core.config.app_logger import logger
# McpClient 客户端封装类，工具的调用都通过这个类来实现
# 注意：MCP 客户端需要和 MCP 服务器在同一台机器上运行
# @Author: xiangjh


class McpClient:
    def __init__(self):
        self.exit_stack = AsyncExitStack()
        self.session: Optional[ClientSession] = None

    # 连接MCP服务器
    async def connect_to_server(self, server_script_path: str):
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")

        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )

        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

        await self.session.initialize()

        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        logger.info("Connected to server with tools:{}", [tool.name for tool in tools])

    async def connect_to_server_url(self, url: str):
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """

        sse_transport = await self.exit_stack.enter_async_context(sse_client(url=url))
        self.write_stream = sse_transport[1]
        self.read_stream = sse_transport[0]
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream,self.write_stream))

        await self.session.initialize()

        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        logger.info("Connected to sse server with tools:{}", [tool.name for tool in tools])    

    # 获取服务器可用工具列表
    async def list_tools(self) -> list:
        """获取服务器可用工具列表"""
        response = await self.session.list_tools()
        return [{
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.inputSchema
        } for tool in response.tools]

    async def execute_tool(self, tool_name: str, params: dict):
        """执行工具调用"""
        logger.info(f"正在执行工具：{tool_name}, 参数：{params}")
        result = await self.session.call_tool(tool_name, params)
        return result