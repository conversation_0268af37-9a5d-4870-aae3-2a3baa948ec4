from typing import List
from mcpclient.mcp_client import Mcp<PERSON><PERSON>
from core.config.app_logger import logger
from core.config.app_config import config

# Mcp工厂类类
# 实现多协议 MCP 客户端的动态初始化与管理，主要功能：
#    - 配置驱动的客户端初始化
#    - 多协议支持（标准IO/SSE）
#    - 连接生命周期管理
#    - 异常熔断
# @Author: xiangjh
class McpFactory:
    @classmethod
    async def create_mcp_clients(cls) -> List[McpClient]:
        """MCP 客户端集群构建器
        
        执行流程：
        1. 解析全局配置中的服务器列表
        2. 过滤已启用服务器节点
        3. 根据协议类型初始化对应客户端
        4. 维护客户端连接池

        Returns:
            List[McpClient]: 已成功建立连接的客户端实例集合

        Raises:
            ConnectionError: 当所有客户端初始化失败时抛出

        Example:
            >>> clients = await McpFactory.create_mcp_clients()
            >>> len(clients) > 0
            True
        """
        servers = config.mcp_servers
        clients = []
        
        for server in servers:
            if not server.enabled:
                logger.info(f"MCPClient {server.key} 未启用")
                continue

            try:
                client = McpClient()
                if server.type == "stdio":
                    await client.connect_to_server(server.url)
                elif server.type == "sse":
                    await client.connect_to_server_url(server.url)
                
                clients.append(client)
                logger.info(f"MCPClient {server.key} 初始化成功")
            except Exception as e:
                logger.error(f"MCPClient {server.key} 初始化失败: {str(e)}", exc_info=True)
                continue

        return clients