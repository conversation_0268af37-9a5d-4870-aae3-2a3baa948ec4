import json
from typing import AsyncGenerator, Dict, Any, Tuple
from langchain_core.messages import AIMessage,HumanMessage
from langchain_core.runnables.history import RunnableWithMessageHistory
from utils.common_utils import CommonUtils
safe_json_dumps = CommonUtils.safe_json_dumps
from core.config.app_logger import logger
#import logging

#logging.basicConfig(level=logging.INFO)
#logger = logging.getLogger(__name__)

# agent 事件处理器，统一返回格式化的 JSON 字符串
# <AUTHOR> xiangjh


async def process_agent_stream(
    chain_with_history: RunnableWithMessageHistory,
    input_context: Dict[str, Any],
    config: Dict[str, Any],
    history,
    question: str,
    session_id: str
) -> AsyncGenerator[str, None]:
    """
    处理 LangChain Agent 的流式事件，统一返回格式化的 JSON 字符串响应。
    
    Args:
        chain_with_history (RunnableWithMessageHistory): 带历史记录的链实例
        input_context (Dict[str, Any]): 输入上下文（input + chat_history）
        config (Dict[str, Any]): 配置信息（session_id 等）
        history: 当前会话的历史记录对象
        question (str): 用户输入的问题
        session_id (str): 会话 ID
    
    Yields:
        str: 格式化的 JSON 字符串
    """
    main_run_id = None

    # 直接使用 astream_events 获取原始事件流
    async for event in chain_with_history.astream_events(input_context, config=config, version="v1"):
        event_type = event["event"]
        current_run_id = event.get("run_id")

        if main_run_id is None:
            main_run_id = current_run_id

        # 只关注主 run_id 的结束事件
        if event_type == "on_chain_end" and current_run_id == main_run_id:
            result = event["data"].get("output", {})
            final_answer = ""
            if isinstance(result, dict):
                final_answer = result.get("output", "[未找到有效输出]")
            else:
                final_answer = str(result) if result else "[空响应]"

            if final_answer in ["[]", "{}", "", "[空响应]", "[未找到有效输出]"]:
                logger.info("忽略无效输出")
                yield safe_json_dumps({
                    "type": "error",
                    "message": "大模型无响应，请稍后再试。"
                })
                continue

            last_ai_msg = None
            messages = await history.aget_messages()
            for msg in messages:
                if isinstance(msg, AIMessage):
                    last_ai_msg = msg.content
                    break

            yield safe_json_dumps({
                "type": "response",
                "content": final_answer
            })

        elif event_type == "on_tool_start":
            yield safe_json_dumps({
                "type": "tool_start",
                "tool": event["name"],
                "input": event["data"].get("input")
            })
            yield safe_json_dumps({
                "type": "tool_start_notic",
                "tool": event["name"],
                "input": event["data"].get("input")
            })

        elif event_type == "on_tool_end":
            output = event["data"].get("output", "")
            tool_name = event["name"]

            yield safe_json_dumps({
                "type": "tool_result",
                "tool": tool_name,
                "output": str(output)
            })

        elif event_type == "on_chat_model_stream":
            delta = event["data"]["chunk"].content
            if delta:
                yield safe_json_dumps({
                    "type": "stream",
                    "content": delta
                })