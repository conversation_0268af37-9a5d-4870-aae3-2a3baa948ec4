import asyncio
from uuid import uuid4
from datetime import datetime, timezone
import threading
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from core.config import config

from fastapi import APIRouter, Body, Depends, FastAPI
from fastapi.responses import StreamingResponse


from core.config.security import check_user
from core.vo.user_vo import UserVO
from core.message.transmitter import Transmitter
from core.message.types import MessageType, MessagePackage
from core.services.database.schemas.message import MessageCreate
from core.services.database.schemas.conversation import ConversationCreate
from core.services.database import db_manager
from core.services.database.crud.conversation import conversation_curd
from core.services.database.crud.message import message_curd
from .messages.markdown import create_markdown_message_chunks
from .messages.choices import radio_choices_message_chunks, select_message_chunks
from .messages.mermaid import create_mermaid_message_chunks
from .messages.thinking import create_thinking_message_chunks
from .messages.tool import create_tool_message_chunks
from .messages.error import create_error_message_chunks
from .messages.state import create_message_state_message_chunks
from .messages.thought_chain import thought_chain_event_generator
from .messages.form import create_form_message_chunks
from .messages.test import create_test_message_chunks
from .messages.widget import create_widget_message_chunks
from utils.redis_util import redis_client

chunks = {
    "markdown": create_markdown_message_chunks(),
    "radio_choices": radio_choices_message_chunks,
    "select_choices": select_message_chunks,
    "mermaid": create_mermaid_message_chunks(),
    "thinking": create_thinking_message_chunks(),
    "tool": create_tool_message_chunks(),
    "error": create_error_message_chunks(),
    "state": create_message_state_message_chunks(),
    "thought_chain": None,
    "form": create_form_message_chunks(),
    "test": create_test_message_chunks(),
    "widget": create_widget_message_chunks(),
}


async def default_event_generator(transmitter: Transmitter, message_type: str = "all"):
    mock_message_chunks = chunks[message_type]
    yield transmitter.start()
    await asyncio.sleep(0.1)
    for chunk in mock_message_chunks:
        if chunk:
            if chunk.get("is_network_error") is True:
                return
            if chunk.get("is_error") is True:
                yield transmitter.send_error(chunk["data"])
                continue

            # Convert the chunk to JSON and format as SSE
            yield transmitter.send_message(
                data=chunk["data"],
                package_type=chunk["package_type"],
                is_last=chunk["is_last"],
                is_new_package=chunk["is_new_package"],
            )
        await asyncio.sleep(0.1)

    # Send a final message to indicate completion
    yield await transmitter.end()
    yield "[DONE]"


async def message_generator_task(
    task_id: str, message_type: str, transmitter: Transmitter
):
    generator = (
        thought_chain_event_generator
        if message_type == "thought_chain"
        else default_event_generator
    )

    async for msg in generator(transmitter=transmitter, message_type=message_type):
        redis_client.client.lpush(task_id, msg)


async def run_task_async(task_id: str, message_type: str, conversation_id: str, agent_code: str):
    transmitter = Transmitter(
        conversation_id=conversation_id,
        message_id=uuid4().hex,
        agent_code=agent_code,
    )
    try:
        await message_generator_task(task_id, message_type, transmitter)
    except Exception as e:
        print(f"Task error: {e}")
        await asyncio.to_thread(redis_client.client.lpush, task_id, "[ERROR]")

def run_task_sync(task_id: str, message_type: str, conversation_id: str, agent_code: str):
    """Run task in thread with synchronous database operations"""
    def sync_task():
        # Create synchronous database connection for this thread
        sync_url = config.database.url.replace("postgresql+asyncpg://", "postgresql://")
        sync_engine = create_engine(
            sync_url,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        SessionLocal = sessionmaker(bind=sync_engine)
        session = SessionLocal()
        
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            transmitter = Transmitter(
                conversation_id=conversation_id,
                message_id=uuid4().hex,
                agent_code=agent_code,
                use_sync_db=True,
                sync_session=session
            )
            
            loop.run_until_complete(message_generator_task(task_id, message_type, transmitter))
            
        except Exception as e:
            print(f"Task error: {e}")
            redis_client.client.lpush(task_id, "[ERROR]")
        finally:
            session.close()
            loop.close()
    
    # Run in a separate thread
    thread = threading.Thread(target=sync_task)
    thread.daemon = True  # Thread will die when main program exits
    thread.start()

def run_task(task_id: str, message_type: str, conversation_id: str, agent_code: str):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        loop.run_until_complete(run_task_async(task_id, message_type, conversation_id, agent_code))
    finally:
        loop.close()


def subscribe_message(task_id):
    _, message = redis_client.client.brpop(task_id, 0)
    print(f"message: {task_id} / {message}")
    return message


async def event_generator(task_id: str):
    if redis_client.client.exists(task_id):
        while True:
            message = await asyncio.to_thread(subscribe_message, task_id)
            if message == "[DONE]":
                break
            else:
                yield message

class TestAgent:
    app: FastAPI
    router: APIRouter

    def __init__(self, app: FastAPI):
        self.app = app
        self.router = APIRouter()
        self.add_api(self.router)
        app.include_router(self.router, tags=["Test"])

    def add_api(self, router: APIRouter):
        @router.post("/api/chat/test")
        async def test(
            data: dict = Body(..., description="用户提问内容"),
            user: UserVO = Depends(check_user),
        ):
            print(data)
            agent_code = data["agent_code"]
            # 根据conversation_id查询会话，如果没有则创建一个
            conversation_id = data.get("conversation_id")
            if not conversation_id:
                async with db_manager.session() as session:
                    new_conversation = ConversationCreate(
                        user_id=user.userId,
                        title="test",
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        current_agent_code=agent_code,
                    )
                    new_conversation = await conversation_curd.create(
                        db=session, obj_input=new_conversation
                    )
                    conversation_id = new_conversation.id

            # 保存用户消息
            message_pkg = MessagePackage(
                package_id=0,
                package_type=0,
                status=1,
                data=data["message"],
            )
            async with db_manager.session() as session:
                new_message = MessageCreate(
                    agent_code=agent_code,
                    conversation_id=conversation_id,
                    message_type=MessageType.HUMAN,
                    content=[message_pkg.model_dump()],
                )
                await message_curd.create(db=session, obj_input=new_message)

            message_type = self.get_message_type(data["message"])

            task_id = f"message-task:{uuid4().hex}"
            redis_client.client.lpush(task_id, "[START]")
            redis_client.client.expire(task_id, 60 * 60 * 24)

            # Use the new sync task runner
            run_task_sync(task_id, message_type, conversation_id, agent_code)

            return StreamingResponse(
                event_generator(task_id=task_id),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                },
            )

    def get_message_type(self, user_message: str):
        msg = user_message.lower().strip()

        types = chunks.keys()
        for msg_type in types:
            if msg_type in msg:
                return msg_type
        return "radio_choices"
