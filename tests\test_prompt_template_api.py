"""
测试提示词模板API端点
"""
import asyncio
import httpx
import json
from typing import Dict, Any


class PromptTemplateAPITester:
    """提示词模板API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", auth_token: str = None):
        self.base_url = base_url
        self.auth_token = auth_token
        self.headers = {}
        if auth_token:
            self.headers["Authorization"] = f"Bearer {auth_token}"
    
    async def test_create_template(self) -> Dict[str, Any]:
        """测试创建模板"""
        template_data = {
            "type": "user",
            "title": "API测试模板",
            "description": "这是通过API创建的测试模板",
            "prompt": "请分析以下内容：{content}",
            "group": "api_test",
            "preview": "内容分析模板",
            "order": 1
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/prompt-template",
                json=template_data,
                headers=self.headers
            )
            print(f"创建模板响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"创建的模板ID: {result.get('id')}")
                return result
            else:
                print(f"创建失败: {response.text}")
                return None
    
    async def test_get_templates(self, template_type: str = None, group: str = None) -> list:
        """测试获取模板列表"""
        params = {}
        if template_type:
            params["template_type"] = template_type
        if group:
            params["group"] = group
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/prompt-template",
                params=params,
                headers=self.headers
            )
            print(f"获取模板列表响应状态: {response.status_code}")
            if response.status_code == 200:
                templates = response.json()
                print(f"获取到 {len(templates)} 个模板")
                return templates
            else:
                print(f"获取失败: {response.text}")
                return []
    
    async def test_get_template_by_id(self, template_id: int) -> Dict[str, Any]:
        """测试根据ID获取模板"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/prompt-template/{template_id}",
                headers=self.headers
            )
            print(f"获取模板详情响应状态: {response.status_code}")
            if response.status_code == 200:
                template = response.json()
                print(f"模板标题: {template.get('title')}")
                return template
            else:
                print(f"获取失败: {response.text}")
                return None
    
    async def test_update_template(self, template_id: int) -> Dict[str, Any]:
        """测试更新模板"""
        update_data = {
            "title": "更新后的API测试模板",
            "description": "这是通过API更新的测试模板"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"{self.base_url}/api/prompt-template/{template_id}",
                json=update_data,
                headers=self.headers
            )
            print(f"更新模板响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"更新后的标题: {result.get('title')}")
                return result
            else:
                print(f"更新失败: {response.text}")
                return None
    
    async def test_get_groups(self) -> list:
        """测试获取分组列表"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/prompt-template/groups",
                headers=self.headers
            )
            print(f"获取分组列表响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                groups = result.get("data", [])
                print(f"获取到 {len(groups)} 个分组: {groups}")
                return groups
            else:
                print(f"获取失败: {response.text}")
                return []
    
    async def test_delete_template(self, template_id: int) -> bool:
        """测试删除模板"""
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{self.base_url}/api/prompt-template/{template_id}",
                headers=self.headers
            )
            print(f"删除模板响应状态: {response.status_code}")
            if response.status_code == 200:
                print("模板删除成功")
                return True
            else:
                print(f"删除失败: {response.text}")
                return False
    
    async def run_full_test(self):
        """运行完整的API测试"""
        print("=== 开始提示词模板API测试 ===")
        
        # 1. 创建模板
        print("\n1. 测试创建模板")
        created_template = await self.test_create_template()
        if not created_template:
            print("创建模板失败，停止测试")
            return
        
        template_id = created_template["id"]
        
        # 2. 获取模板列表
        print("\n2. 测试获取模板列表")
        await self.test_get_templates()
        
        # 3. 按类型获取模板
        print("\n3. 测试按类型获取模板")
        await self.test_get_templates(template_type="user")
        
        # 4. 按分组获取模板
        print("\n4. 测试按分组获取模板")
        await self.test_get_templates(group="api_test")
        
        # 5. 获取模板详情
        print("\n5. 测试获取模板详情")
        await self.test_get_template_by_id(template_id)
        
        # 6. 更新模板
        print("\n6. 测试更新模板")
        await self.test_update_template(template_id)
        
        # 7. 获取分组列表
        print("\n7. 测试获取分组列表")
        await self.test_get_groups()
        
        # 8. 删除模板
        print("\n8. 测试删除模板")
        await self.test_delete_template(template_id)
        
        print("\n=== API测试完成 ===")


async def main():
    """主函数"""
    # 注意：这里需要有效的认证token才能测试API
    # 在实际测试中，你需要先登录获取token
    tester = PromptTemplateAPITester(
        base_url="http://localhost:8000",
        auth_token="your-auth-token-here"  # 替换为实际的token
    )
    
    try:
        await tester.run_full_test()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")


if __name__ == "__main__":
    print("提示词模板API测试脚本")
    print("注意：运行此脚本前请确保：")
    print("1. 服务器正在运行 (python main.py)")
    print("2. 数据库迁移已完成 (alembic upgrade head)")
    print("3. 已设置有效的认证token")
    print()
    
    # 取消注释下面的行来运行测试
    # asyncio.run(main())
