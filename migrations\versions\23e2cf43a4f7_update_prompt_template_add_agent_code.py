"""update prompt_template add agent_code

Revision ID: 23e2cf43a4f7
Revises: 4702be57162c
Create Date: 2025-07-14 18:51:11.738198

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '23e2cf43a4f7'
down_revision: Union[str, None] = '4702be57162c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.Column('agent_code', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=""))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prompt_template', 'agent_code')
    # ### end Alembic commands ###
