from typing import List, Optional
from datetime import datetime, timezone
from uuid import uuid4

from sqlmodel import Field, SQLModel
from sqlalchemy import Column, DateTime
from sqlalchemy.dialects.postgresql import JSONB, TSVECTOR


class MessageBase(SQLModel):
    """聊天消息基础模型"""

    id: str = Field(default_factory=lambda: uuid4().hex, primary_key=True)
    agent_code: Optional[str] = Field(default=None, index=True, description="智能体ID")
    conversation_id: str = Field(index=True, description="对话会话ID")
    message_type: str = Field(description="消息类型，ai 或 human", default="ai")
    content: List[dict] = Field(sa_column=Column(JSONB), description="消息内容")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), sa_column=Column(DateTime(timezone=True)), description="创建时间"
    )
    # 评价相关字段
    like_count: int = Field(default=0, description="同意数量")
    dislike_count: int = Field(default=0, description="反对数量")
    user_rating: Optional[str] = Field(default=None, description="用户评价状态: like, dislike 或 null")
    data_object: Optional[dict] = Field(default=None,sa_column=Column(JSONB, default=None, nullable=True),description="自定义数据存储")
    content_tsvector: Optional[str] = Field(default=None, sa_column=Column(TSVECTOR, nullable=True), description="内容全文搜索向量")


class MessageTable(MessageBase, table=True):
    """数据库中的消息模型"""

    __tablename__ = "message"


class MessageCreate(MessageBase):
    """创建消息的模型"""


class MessageRead(MessageBase):
    """API响应中的消息模型"""


class MessageUpdate(SQLModel):
    """更新消息的模型"""

    agent_code: Optional[str] = None
    message_type: Optional[str] = None
    content: Optional[str] = None
    like_count: Optional[int] = None
    dislike_count: Optional[int] = None
    user_rating: Optional[str] = None
    data_object: Optional[dict] = None
    
