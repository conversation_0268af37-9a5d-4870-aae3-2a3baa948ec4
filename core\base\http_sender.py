from typing import AsyncIterator, Dict, Any, Optional
from fastapi import Request
from llmclient.http_client import HttpClient
from core.base.base_sender import BaseSender
from core.config.app_logger import logger

# Http 发送器基类，用于封装非流式响应生成逻辑。 
# <AUTHOR> xiangjh
class HttpBaseSender(BaseSender):
    """
    用于封装基于 HTTP 同步请求的大模型响应生成逻辑，适用于非流式场景。
    支持以下功能：
    - 会话 ID 生成
    - 请求构建与发送
    - 响应结果解析与封装
    - 错误处理与日志记录
    
    Attributes:
        agent_code (str): 当前 Agent 的唯一标识符，用于会话 ID 生成及日志上下文识别。
        client (HttpClient): HTTP 客户端实例，用于执行同步查询。
    """
    def __init__(self, 
                 agent_code: str, 
                 base_url: str = None):
        """
        初始化 HttpBaseSender 实例。

        Args:
            agent_code (str): 当前 Agent 的唯一标识符，用于：
                - 生成会话 ID（session_id）
                - 日志记录上下文识别
                - 区分不同 Agent 的行为逻辑（可选）
            base_url (str): HTTP 请求的根 URL    
        """
        super().__init__(agent_code)
        self.client = HttpClient(base_url=base_url,agent_code=agent_code)

    async def _generate_content(self,
        request: Request,
        question: str,
        user_id: str,
        conversation_id: str,
        extend_params: dict
    ) -> AsyncIterator[bytes]:
        """
        此方法调用 `self.client.http_query(...)` 执行同步查询，
        并使用 [_process_result(...)] 对结果进行格式转换后逐个 yield 发送。
        
        每个产出项应为一个字典对象，格式如下：
            {
                "data": str,           # 需要发送的数据内容
                "package_type": int,   # 消息类型编码（如：0 表示普通文本）
                "is_last": bool        # 是否为最后一个数据块
            }
            
        注意：由于是同步查询，本方法只会产出一次响应数据，并标记 is_last=True。
        
        Args:
            request (Request): FastAPI 请求对象。
            question (str): 用户输入的问题文本。
            user_id (str): 用户唯一标识符。
            conversation_id (str): 会话唯一标识符。
            
        Yields:
            AsyncIterator[bytes]: 异步生成的消息内容片段。尽管是同步查询，仍统一返回异步迭代器格式。
            
        Raises:
            Exception: 在执行 http_query 过程中若发生异常，将被捕获并记录日志。
        """
        try:
            logger.info(f"开始执行同步查询")
            
            payload = self._get_payload(question, extend_params)
            headers = self._get_headers()

            result = await self.client.http_query(headers,payload)
            logger.info(f"同步查询结果: {result}")
            content = self._process_result(result)

            yield {
                    "data": content,
                    "is_last": True,
                    "package_type": 0
                }
            
        except Exception as e:
            errmsg = str(e)
            logger.error(f"http处理失败: {errmsg}", exc_info=True)
            yield {
                    "data": f"调用接口失败，请稍后重试！",
                    "is_last": True,
                    "package_type": 0
                }
            return
        
    def _get_payload(self, question: str, extend_params: dict) -> dict:
        """供子类重写的payload生成方法"""
        return extend_params  # 默认直接返回扩展参数

    def _get_headers(self) -> dict:
        """供子类重写的headers生成方法"""
        return {
            "Content-Type": "application/json"
        }
    
    def _process_result(self, result: Dict[str, Any]) -> Optional[str]:
        """
        处理 HTTP 查询返回的结果。
        
        默认实现根据 result['success'] 字段判断是否成功：
        - 成功：返回 SQL 及其解释
        - 失败：返回错误信息
        
        子类可重写此方法以支持更复杂的响应格式。
        
        Args:
            result (Dict[str, Any]): HTTP 请求返回的原始结果字典。
            
        Returns:
            Optional[str]: 格式化后的字符串内容，若无法处理则返回 None。
        """
        if result.get("success"):
            content = result['data']
        else:
            content = f"错误：{result.get('error', '未知错误')}"

        return content        