"""
测试用户提示词模板API端点
"""
import asyncio
import httpx
import json
from typing import Dict, Any, List


class UserPromptTemplateAPITester:
    """用户提示词模板API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", auth_token: str = None):
        self.base_url = base_url
        self.auth_token = auth_token
        self.headers = {}
        if auth_token:
            self.headers["Authorization"] = f"Bearer {auth_token}"
    
    async def test_create_user_template(self) -> Dict[str, Any]:
        """测试创建用户模板"""
        template_data = {
            "title": "用户API测试模板",
            "description": "这是通过API创建的用户测试模板",
            "prompt": "请分析以下内容：{content}",
            "group": "user_api_test",
            "preview": "用户内容分析模板",
            "order": 1
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/user-templates",
                json=template_data,
                headers=self.headers
            )
            print(f"创建用户模板响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"创建的用户模板ID: {result['data']['id']}")
                return result['data']
            else:
                print(f"创建失败: {response.text}")
                return None
    
    async def test_get_user_templates(self) -> List[Dict[str, Any]]:
        """测试获取用户模板列表"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/user-templates",
                headers=self.headers
            )
            print(f"获取用户模板列表响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                templates = result.get('data', [])
                print(f"获取到 {len(templates)} 个用户模板")
                return templates
            else:
                print(f"获取失败: {response.text}")
                return []
    
    async def test_get_user_template_by_id(self, template_id: int) -> Dict[str, Any]:
        """测试根据ID获取用户模板"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/user-templates/{template_id}",
                headers=self.headers
            )
            print(f"获取用户模板详情响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                template = result.get('data', {})
                print(f"用户模板标题: {template.get('title')}")
                return template
            else:
                print(f"获取失败: {response.text}")
                return None
    
    async def test_update_user_template(self, template_id: int) -> Dict[str, Any]:
        """测试更新用户模板"""
        update_data = {
            "title": "更新后的用户API测试模板",
            "description": "这是通过API更新的用户测试模板"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"{self.base_url}/api/user-templates/{template_id}",
                json=update_data,
                headers=self.headers
            )
            print(f"更新用户模板响应状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                template = result.get('data', {})
                print(f"更新后的标题: {template.get('title')}")
                return template
            else:
                print(f"更新失败: {response.text}")
                return None
    
    async def test_delete_user_template(self, template_id: int) -> bool:
        """测试删除用户模板"""
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{self.base_url}/api/user-templates/{template_id}",
                headers=self.headers
            )
            print(f"删除用户模板响应状态: {response.status_code}")
            if response.status_code == 200:
                print("用户模板删除成功")
                return True
            else:
                print(f"删除失败: {response.text}")
                return False
    
    async def test_unauthorized_access(self, template_id: int) -> None:
        """测试未授权访问（使用不同用户的token）"""
        # 这个测试需要使用另一个用户的token
        # 在实际测试中，你需要准备两个不同用户的token
        unauthorized_headers = {
            "Authorization": "Bearer another-user-token-here"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/user-templates/{template_id}",
                headers=unauthorized_headers
            )
            print(f"未授权访问响应状态: {response.status_code}")
            if response.status_code == 404:
                print("权限验证成功：未授权用户无法访问其他用户的模板")
            else:
                print(f"权限验证失败: {response.text}")
    
    async def run_full_test(self):
        """运行完整的用户模板API测试"""
        print("=== 开始用户提示词模板API测试 ===")
        
        # 1. 创建用户模板
        print("\n1. 测试创建用户模板")
        created_template = await self.test_create_user_template()
        if not created_template:
            print("创建用户模板失败，停止测试")
            return
        
        template_id = created_template["id"]
        
        # 2. 获取用户模板列表
        print("\n2. 测试获取用户模板列表")
        await self.test_get_user_templates()
        
        # 3. 获取用户模板详情
        print("\n3. 测试获取用户模板详情")
        await self.test_get_user_template_by_id(template_id)
        
        # 4. 更新用户模板
        print("\n4. 测试更新用户模板")
        await self.test_update_user_template(template_id)
        
        # 5. 测试未授权访问
        print("\n5. 测试未授权访问")
        print("注意：此测试需要另一个用户的token才能完成")
        # await self.test_unauthorized_access(template_id)
        
        # 6. 删除用户模板
        print("\n6. 测试删除用户模板")
        await self.test_delete_user_template(template_id)
        
        print("\n=== 用户模板API测试完成 ===")


async def main():
    """主函数"""
    # 注意：这里需要有效的认证token才能测试API
    # 在实际测试中，你需要先登录获取token
    tester = UserPromptTemplateAPITester(
        base_url="http://localhost:8000",
        auth_token="your-auth-token-here"  # 替换为实际的token
    )
    
    try:
        await tester.run_full_test()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")


if __name__ == "__main__":
    print("用户提示词模板API测试脚本")
    print("注意：运行此脚本前请确保：")
    print("1. 服务器正在运行 (python main.py)")
    print("2. 数据库迁移已完成 (alembic upgrade head)")
    print("3. 已设置有效的认证token")
    print()
    
    # 取消注释下面的行来运行测试
    # asyncio.run(main())
